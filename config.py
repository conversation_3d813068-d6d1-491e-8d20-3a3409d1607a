# config.py - 项目配置文件
import os
from typing import Optional

class Config:
    """应用配置类"""
    
    # =================== 数据库配置 ===================
    # MariaDB配置
    MARIADB_USER = os.environ.get('MARIADB_USER', 'root')
    MARIADB_PASSWORD = os.environ.get('MARIADB_PASSWORD', 'rw80827')
    MARIADB_HOST = os.environ.get('MARIADB_HOST', '*************')
    MARIADB_PORT = os.environ.get('MARIADB_PORT', '3306')
    MARIADB_MODEL_DB = os.environ.get('MARIADB_MODEL_DB', 'model_registry')
    MARIADB_CHAT_DB = os.environ.get('MARIADB_CHAT_DB', 'chat_system')
    
    # =================== API服务配置 ===================
    # Model API Service (端口5002) - 模型和聊天API
    MODEL_API_BASE_URL = os.environ.get('MODEL_API_BASE_URL', 'http://localhost:5002')
    MODEL_API_KEY = os.environ.get('MODEL_API_KEY', 'sk-default-api-key-change-in-production')
    
    # Record API Service (端口5003) - 用户、对话、消息记录API
    RECORD_API_BASE_URL = os.environ.get('RECORD_API_BASE_URL', 'http://localhost:5003')
    RECORD_API_KEY = os.environ.get('RECORD_API_KEY', 'admin-api-key-change-in-production')
    
    # =================== 应用配置 ===================
    # Dash应用配置
    DASH_HOST = os.environ.get('DASH_HOST', '0.0.0.0')
    DASH_PORT = int(os.environ.get('DASH_PORT', '8050'))
    DASH_DEBUG = os.environ.get('DASH_DEBUG', 'True').lower() == 'true'
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FORMAT = os.environ.get('LOG_FORMAT', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # =================== 安全配置 ===================
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-secret-key-change-in-production')
    JWT_ALGORITHM = os.environ.get('JWT_ALGORITHM', 'HS256')
    JWT_EXPIRATION_HOURS = int(os.environ.get('JWT_EXPIRATION_HOURS', '24'))
    
    # =================== 功能开关 ===================
    # 是否启用API模式（使用Record API而不是直接数据库访问）
    USE_RECORD_API = os.environ.get('USE_RECORD_API', 'True').lower() == 'true'
    
    # 是否启用缓存
    ENABLE_CACHE = os.environ.get('ENABLE_CACHE', 'True').lower() == 'true'
    
    # 是否启用调试模式
    DEBUG_MODE = os.environ.get('DEBUG_MODE', 'False').lower() == 'true'
    
    @classmethod
    def get_model_api_url(cls) -> str:
        """获取模型API的完整URL"""
        return cls.MODEL_API_BASE_URL.rstrip('/')
    
    @classmethod
    def get_record_api_url(cls) -> str:
        """获取记录API的完整URL"""
        return cls.RECORD_API_BASE_URL.rstrip('/')
    
    @classmethod
    def get_model_api_headers(cls) -> dict:
        """获取模型API请求头"""
        return {
            'Authorization': f'Bearer {cls.MODEL_API_KEY}',
            'Content-Type': 'application/json'
        }
    
    @classmethod
    def get_record_api_headers(cls) -> dict:
        """获取记录API请求头"""
        return {
            'Authorization': f'Bearer {cls.RECORD_API_KEY}',
            'Content-Type': 'application/json'
        }
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置是否完整"""
        required_configs = [
            'MARIADB_HOST',
            'MARIADB_USER', 
            'MARIADB_PASSWORD',
            'MODEL_API_BASE_URL',
            'MODEL_API_KEY',
            'RECORD_API_BASE_URL',
            'RECORD_API_KEY'
        ]
        
        missing_configs = []
        for config in required_configs:
            if not getattr(cls, config):
                missing_configs.append(config)
        
        if missing_configs:
            print(f"缺少必要配置: {', '.join(missing_configs)}")
            return False
        
        return True
    
    @classmethod
    def print_config_summary(cls):
        """打印配置摘要"""
        print("=== 应用配置摘要 ===")
        print(f"数据库主机: {cls.MARIADB_HOST}:{cls.MARIADB_PORT}")
        print(f"模型数据库: {cls.MARIADB_MODEL_DB}")
        print(f"聊天数据库: {cls.MARIADB_CHAT_DB}")
        print(f"模型API: {cls.MODEL_API_BASE_URL}")
        print(f"记录API: {cls.RECORD_API_BASE_URL}")
        print(f"Dash应用: {cls.DASH_HOST}:{cls.DASH_PORT}")
        print(f"使用Record API: {cls.USE_RECORD_API}")
        print(f"调试模式: {cls.DEBUG_MODE}")
        print("==================")


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG_MODE = True
    DASH_DEBUG = True
    LOG_LEVEL = 'DEBUG'


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG_MODE = False
    DASH_DEBUG = False
    LOG_LEVEL = 'INFO'
    
    # 生产环境安全检查
    @classmethod
    def validate_production_security(cls) -> bool:
        """验证生产环境安全配置"""
        security_issues = []
        
        # 检查默认密钥
        if cls.JWT_SECRET_KEY == 'your-secret-key-change-in-production':
            security_issues.append("JWT密钥使用默认值")
        
        if cls.MODEL_API_KEY == 'sk-default-api-key-change-in-production':
            security_issues.append("模型API密钥使用默认值")
            
        if cls.RECORD_API_KEY == 'admin-api-key-change-in-production':
            security_issues.append("记录API密钥使用默认值")
        
        if security_issues:
            print(f"生产环境安全问题: {', '.join(security_issues)}")
            return False
        
        return True


class TestConfig(Config):
    """测试环境配置"""
    DEBUG_MODE = True
    USE_RECORD_API = False  # 测试时可能直接使用数据库
    ENABLE_CACHE = False


# 根据环境变量选择配置
def get_config() -> Config:
    """根据环境变量获取配置类"""
    env = os.environ.get('FLASK_ENV', 'development').lower()
    
    if env == 'production':
        return ProductionConfig
    elif env == 'testing':
        return TestConfig
    else:
        return DevelopmentConfig


# 全局配置实例
config = get_config()

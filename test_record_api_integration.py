#!/usr/bin/env python3
# test_record_api_integration.py - 测试Record API集成
import os
import sys
import logging
import time
from typing import Dict, Any, Optional

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_record_api_client():
    """测试Record API客户端"""
    print("=== 测试Record API客户端 ===")
    
    try:
        from record_api_client import record_api_client
        
        # 1. 健康检查
        print("1. 健康检查...")
        health = record_api_client.health_check()
        print(f"   健康状态: {'✓ 正常' if health else '✗ 异常'}")
        
        if not health:
            print("   Record API服务不可用，请检查服务是否启动")
            return False
        
        # 2. API信息检查
        print("2. API信息检查...")
        api_info = record_api_client.get_api_info()
        if api_info:
            print(f"   ✓ API信息获取成功")
        else:
            print(f"   ✗ API信息获取失败")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Record API客户端测试失败: {e}")
        return False


def test_user_operations():
    """测试用户相关操作"""
    print("\n=== 测试用户操作 ===")
    
    try:
        from record_api_client import record_api_client
        
        # 1. 测试获取用户列表
        print("1. 获取用户列表...")
        users = record_api_client.get_users(page=1, per_page=5)
        if users:
            print(f"   ✓ 获取到 {len(users.get('users', []))} 个用户")
        else:
            print("   ✗ 获取用户列表失败")
            return False
        
        # 2. 测试根据API密钥获取用户
        print("2. 测试API密钥验证...")
        test_api_key = "admin-api-key-change-in-production"
        user = record_api_client.get_user_by_api_key(test_api_key)
        if user:
            print(f"   ✓ API密钥验证成功，用户ID: {user.get('id')}")
            test_user_id = user.get('id')
        else:
            print("   ✗ API密钥验证失败")
            return False
        
        # 3. 测试获取用户统计
        print("3. 获取用户统计...")
        stats = record_api_client.get_user_stats(test_user_id)
        if stats:
            print(f"   ✓ 用户统计获取成功")
        else:
            print("   ✗ 用户统计获取失败")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 用户操作测试失败: {e}")
        return False


def test_conversation_operations():
    """测试对话相关操作"""
    print("\n=== 测试对话操作 ===")
    
    try:
        from record_api_client import record_api_client
        
        # 获取测试用户
        test_api_key = "admin-api-key-change-in-production"
        user = record_api_client.get_user_by_api_key(test_api_key)
        if not user:
            print("   ✗ 无法获取测试用户")
            return False
        
        user_id = user.get('id')
        
        # 1. 创建测试对话
        print("1. 创建测试对话...")
        conversation = record_api_client.create_conversation(
            user_id=user_id,
            title="API集成测试对话"
        )
        if conversation:
            print(f"   ✓ 对话创建成功，ID: {conversation.get('id')}")
            test_conv_id = conversation.get('id')
        else:
            print("   ✗ 对话创建失败")
            return False
        
        # 2. 获取用户对话列表
        print("2. 获取用户对话列表...")
        conversations = record_api_client.get_user_conversations(user_id)
        if conversations:
            conv_count = len(conversations.get('conversations', []))
            print(f"   ✓ 获取到 {conv_count} 个对话")
        else:
            print("   ✗ 获取对话列表失败")
        
        # 3. 更新对话标题
        print("3. 更新对话标题...")
        updated = record_api_client.update_conversation(
            test_conv_id,
            title="API集成测试对话（已更新）"
        )
        if updated:
            print("   ✓ 对话标题更新成功")
        else:
            print("   ✗ 对话标题更新失败")
        
        return test_conv_id
        
    except Exception as e:
        print(f"   ✗ 对话操作测试失败: {e}")
        return None


def test_message_operations(conversation_id: int):
    """测试消息相关操作"""
    print("\n=== 测试消息操作 ===")
    
    try:
        from record_api_client import record_api_client
        
        # 1. 创建测试消息
        print("1. 创建用户消息...")
        user_message = record_api_client.create_message(
            conversation_id=conversation_id,
            role="user",
            content="这是一条API集成测试消息",
            model_id=1
        )
        if user_message:
            print(f"   ✓ 用户消息创建成功，ID: {user_message.get('id')}")
        else:
            print("   ✗ 用户消息创建失败")
            return False
        
        # 2. 创建AI回复消息
        print("2. 创建AI回复消息...")
        ai_message = record_api_client.create_message(
            conversation_id=conversation_id,
            role="assistant",
            content="这是AI的测试回复消息",
            model_id=1,
            prompt_tokens=10,
            completion_tokens=15,
            total_cost=0.001
        )
        if ai_message:
            print(f"   ✓ AI消息创建成功，ID: {ai_message.get('id')}")
        else:
            print("   ✗ AI消息创建失败")
        
        # 3. 获取对话消息列表
        print("3. 获取对话消息列表...")
        messages = record_api_client.get_conversation_messages(conversation_id)
        if messages:
            msg_count = len(messages.get('messages', []))
            print(f"   ✓ 获取到 {msg_count} 条消息")
        else:
            print("   ✗ 获取消息列表失败")
        
        # 4. 更新消息内容
        print("4. 更新消息内容...")
        if user_message:
            updated = record_api_client.update_message(
                user_message.get('id'),
                content="这是更新后的测试消息"
            )
            if updated:
                print("   ✓ 消息内容更新成功")
            else:
                print("   ✗ 消息内容更新失败")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 消息操作测试失败: {e}")
        return False


def test_db_operator_integration():
    """测试db_operator集成"""
    print("\n=== 测试db_operator集成 ===")
    
    try:
        from db_operator import (
            verify_api_key,
            get_user_data,
            get_user_conversations_list,
            create_new_conversation,
            create_message
        )
        
        # 1. 测试API密钥验证
        print("1. 测试API密钥验证...")
        test_api_key = "admin-api-key-change-in-production"
        user = verify_api_key(test_api_key)
        if user:
            print(f"   ✓ API密钥验证成功，用户ID: {user.get('id')}")
            test_user_id = user.get('id')
        else:
            print("   ✗ API密钥验证失败")
            return False
        
        # 2. 测试获取用户数据
        print("2. 测试获取用户数据...")
        user_data = get_user_data(test_user_id)
        if user_data:
            print(f"   ✓ 用户数据获取成功")
        else:
            print("   ✗ 用户数据获取失败")
        
        # 3. 测试获取对话列表
        print("3. 测试获取对话列表...")
        conversations = get_user_conversations_list(test_user_id)
        if isinstance(conversations, list):
            print(f"   ✓ 对话列表获取成功，共 {len(conversations)} 个对话")
        else:
            print("   ✗ 对话列表获取失败")
        
        return True
        
    except Exception as e:
        print(f"   ✗ db_operator集成测试失败: {e}")
        return False


def cleanup_test_data(conversation_id: int):
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    
    try:
        from record_api_client import record_api_client
        
        if conversation_id:
            deleted = record_api_client.delete_conversation(conversation_id)
            if deleted:
                print("   ✓ 测试对话删除成功")
            else:
                print("   ✗ 测试对话删除失败")
        
    except Exception as e:
        print(f"   ✗ 清理测试数据失败: {e}")


def main():
    """主测试函数"""
    print("开始Record API集成测试...")
    print("=" * 50)
    
    # 测试步骤
    test_results = []
    test_conversation_id = None
    
    # 1. 测试Record API客户端
    result = test_record_api_client()
    test_results.append(("Record API客户端", result))
    
    if result:
        # 2. 测试用户操作
        result = test_user_operations()
        test_results.append(("用户操作", result))
        
        if result:
            # 3. 测试对话操作
            test_conversation_id = test_conversation_operations()
            test_results.append(("对话操作", test_conversation_id is not None))
            
            if test_conversation_id:
                # 4. 测试消息操作
                result = test_message_operations(test_conversation_id)
                test_results.append(("消息操作", result))
        
        # 5. 测试db_operator集成
        result = test_db_operator_integration()
        test_results.append(("db_operator集成", result))
    
    # 清理测试数据
    if test_conversation_id:
        cleanup_test_data(test_conversation_id)
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"{test_name:<20} {status}")
        if not passed:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有测试通过！Record API集成成功！")
        return 0
    else:
        print("❌ 部分测试失败，请检查配置和服务状态")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

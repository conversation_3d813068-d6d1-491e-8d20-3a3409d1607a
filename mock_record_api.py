#!/usr/bin/env python3
# mock_record_api.py - 模拟Record API服务用于测试
from flask import Flask, request, jsonify
import json
from datetime import datetime
import uuid

app = Flask(__name__)

# 模拟数据存储
mock_users = {
    1: {
        "id": 1,
        "api_key": "admin-api-key-change-in-production",
        "created_at": "2024-01-01T00:00:00",
        "is_active": True,
        "permission": 9,
        "current_balance": 100.0,
        "total_spent": 5.5,
        "total_prompt_tokens": 1000,
        "total_completion_tokens": 800,
        "current_temperature": 0.7,
        "current_model_name": "gpt-3.5-turbo",
        "current_conversation_id": 1,  # 添加当前对话ID
        "total_deposited": 100.0,  # 添加总充值金额
        "mathjax": False
    }
}

mock_conversations = {
    1: {
        "id": 1,
        "user_id": 1,
        "title": "测试对话",
        "created_at": "2024-01-01T00:00:00",
        "latest_revised_at": "2024-01-01T00:00:00"
    }
}

mock_messages = {
    1: {
        "id": 1,
        "conversation_id": 1,
        "role": "user",
        "content": "你好",
        "created_at": "2024-01-01T00:00:00",
        "model_id": 1,
        "is_error": False
    },
    2: {
        "id": 2,
        "conversation_id": 1,
        "role": "assistant", 
        "content": "你好！我是AI助手，很高兴为您服务。",
        "created_at": "2024-01-01T00:01:00",
        "model_id": 1,
        "is_error": False,
        "prompt_tokens": 5,
        "completion_tokens": 15,
        "total_cost": 0.001
    }
}

# 计数器
next_user_id = 2
next_conversation_id = 2
next_message_id = 3

def success_response(data):
    """成功响应"""
    return jsonify({"success": True, "data": data})

def error_response(message, status_code=400):
    """错误响应"""
    return jsonify({"success": False, "error": message}), status_code

def check_auth():
    """检查认证"""
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return False
    
    token = auth_header.split(' ')[1]
    # 简单验证，实际应该更严格
    return token in ['admin-api-key-change-in-production']

# =================== 系统API ===================

@app.route('/health')
def health():
    return jsonify({"status": "ok"})

@app.route('/api/v1/health')
def api_health():
    return success_response({"status": "ok", "service": "mock-record-api"})

@app.route('/api/v1/info')
def api_info():
    return success_response({
        "service": "Mock Record API",
        "version": "1.0.0",
        "description": "模拟Record API服务用于测试"
    })

# =================== 用户API ===================

@app.route('/api/v1/users/by-api-key/<api_key>')
def get_user_by_api_key(api_key):
    if not check_auth():
        return error_response("Unauthorized", 401)
    
    for user in mock_users.values():
        if user['api_key'] == api_key:
            return success_response(user)
    
    return error_response("User not found", 404)

@app.route('/api/v1/users/<int:user_id>')
def get_user(user_id):
    if not check_auth():
        return error_response("Unauthorized", 401)
    
    user = mock_users.get(user_id)
    if not user:
        return error_response("User not found", 404)
    
    return success_response(user)

@app.route('/api/v1/users', methods=['GET'])
def get_users():
    if not check_auth():
        return error_response("Unauthorized", 401)

    users_list = list(mock_users.values())

    return success_response({
        "users": users_list,
        "pagination": {
            "page": 1,
            "per_page": 20,
            "total": len(users_list),
            "pages": 1
        }
    })

@app.route('/api/v1/users', methods=['POST'])
def create_user():
    if not check_auth():
        return error_response("Unauthorized", 401)
    
    global next_user_id
    data = request.get_json()
    
    new_user = {
        "id": next_user_id,
        "api_key": data.get('api_key'),
        "created_at": datetime.utcnow().isoformat(),
        "is_active": data.get('is_active', True),
        "permission": data.get('permission', 1),
        "current_balance": data.get('current_balance', 0.0),
        "total_spent": 0.0,
        "total_prompt_tokens": 0,
        "total_completion_tokens": 0,
        "current_temperature": data.get('current_temperature', 0.7),
        "mathjax": data.get('mathjax', False)
    }
    
    mock_users[next_user_id] = new_user
    next_user_id += 1
    
    return success_response(new_user)

@app.route('/api/v1/users/<int:user_id>', methods=['PUT'])
def update_user(user_id):
    if not check_auth():
        return error_response("Unauthorized", 401)

    user = mock_users.get(user_id)
    if not user:
        return error_response("User not found", 404)

    data = request.get_json()

    # 更新用户字段
    if 'is_active' in data:
        user['is_active'] = data['is_active']
    if 'permission' in data:
        user['permission'] = data['permission']
    if 'current_balance' in data:
        user['current_balance'] = data['current_balance']
    if 'total_deposited' in data:
        user['total_deposited'] = data['total_deposited']
    if 'current_conversation_id' in data:
        user['current_conversation_id'] = data['current_conversation_id']
    if 'current_temperature' in data:
        user['current_temperature'] = data['current_temperature']
    if 'current_model_name' in data:
        user['current_model_name'] = data['current_model_name']
    if 'mathjax' in data:
        user['mathjax'] = data['mathjax']

    return success_response(user)

@app.route('/api/v1/users/<int:user_id>/stats')
def get_user_stats(user_id):
    if not check_auth():
        return error_response("Unauthorized", 401)

    user = mock_users.get(user_id)
    if not user:
        return error_response("User not found", 404)

    # 计算统计信息
    user_conversations = [c for c in mock_conversations.values() if c['user_id'] == user_id]
    user_messages = [m for m in mock_messages.values()
                    if any(c['id'] == m['conversation_id'] for c in user_conversations)]

    stats = {
        "user_data": user,
        "conversation_count": len(user_conversations),
        "message_count": len(user_messages),
        "current_model": {"name": user.get("current_model_name", "gpt-3.5-turbo")}
    }

    return success_response(stats)

# =================== 对话API ===================

@app.route('/api/v1/conversations/<int:conversation_id>')
def get_conversation(conversation_id):
    if not check_auth():
        return error_response("Unauthorized", 401)
    
    conversation = mock_conversations.get(conversation_id)
    if not conversation:
        return error_response("Conversation not found", 404)
    
    return success_response(conversation)

@app.route('/api/v1/conversations', methods=['POST'])
def create_conversation():
    if not check_auth():
        return error_response("Unauthorized", 401)

    global next_conversation_id
    data = request.get_json()

    new_conversation = {
        "id": next_conversation_id,
        "user_id": data.get('user_id'),
        "title": data.get('title', '新对话'),
        "created_at": datetime.utcnow().isoformat(),
        "latest_revised_at": datetime.utcnow().isoformat()
    }

    mock_conversations[next_conversation_id] = new_conversation
    next_conversation_id += 1

    return success_response(new_conversation)

@app.route('/api/v1/conversations/<int:conversation_id>', methods=['PUT'])
def update_conversation(conversation_id):
    if not check_auth():
        return error_response("Unauthorized", 401)

    conversation = mock_conversations.get(conversation_id)
    if not conversation:
        return error_response("Conversation not found", 404)

    data = request.get_json()

    # 更新对话字段
    if 'title' in data:
        conversation['title'] = data['title']
    if 'latest_revised_at' in data:
        conversation['latest_revised_at'] = data['latest_revised_at']
    else:
        conversation['latest_revised_at'] = datetime.utcnow().isoformat()

    return success_response(conversation)

@app.route('/api/v1/conversations/<int:conversation_id>', methods=['DELETE'])
def delete_conversation(conversation_id):
    if not check_auth():
        return error_response("Unauthorized", 401)

    if conversation_id in mock_conversations:
        del mock_conversations[conversation_id]
        # 删除相关消息
        messages_to_delete = [mid for mid, msg in mock_messages.items()
                            if msg['conversation_id'] == conversation_id]
        for mid in messages_to_delete:
            del mock_messages[mid]
        return success_response({"message": "Conversation deleted"})
    else:
        return error_response("Conversation not found", 404)

@app.route('/api/v1/users/<int:user_id>/conversations')
def get_user_conversations(user_id):
    if not check_auth():
        return error_response("Unauthorized", 401)
    
    user_conversations = [c for c in mock_conversations.values() if c['user_id'] == user_id]
    
    return success_response({
        "conversations": user_conversations,
        "pagination": {
            "page": 1,
            "per_page": 20,
            "total": len(user_conversations),
            "pages": 1
        }
    })

@app.route('/api/v1/conversations/<int:conversation_id>/messages')
def get_conversation_messages(conversation_id):
    if not check_auth():
        return error_response("Unauthorized", 401)
    
    conversation_messages = [m for m in mock_messages.values() if m['conversation_id'] == conversation_id]
    conversation_messages.sort(key=lambda x: x['created_at'])
    
    return success_response({
        "messages": conversation_messages,
        "pagination": {
            "page": 1,
            "per_page": 100,
            "total": len(conversation_messages),
            "pages": 1
        }
    })

# =================== 消息API ===================

@app.route('/api/v1/messages', methods=['POST'])
def create_message():
    if not check_auth():
        return error_response("Unauthorized", 401)
    
    global next_message_id
    data = request.get_json()
    
    new_message = {
        "id": next_message_id,
        "conversation_id": data.get('conversation_id'),
        "role": data.get('role'),
        "content": data.get('content'),
        "created_at": datetime.utcnow().isoformat(),
        "model_id": data.get('model_id', 1),
        "is_error": data.get('is_error', False),
        "prompt_tokens": data.get('prompt_tokens', 0),
        "completion_tokens": data.get('completion_tokens', 0),
        "total_cost": data.get('total_cost', 0.0)
    }
    
    mock_messages[next_message_id] = new_message
    next_message_id += 1
    
    return success_response(new_message)

@app.route('/api/v1/messages/<int:message_id>')
def get_message(message_id):
    if not check_auth():
        return error_response("Unauthorized", 401)

    message = mock_messages.get(message_id)
    if not message:
        return error_response("Message not found", 404)

    return success_response(message)

@app.route('/api/v1/messages/<int:message_id>', methods=['PUT'])
def update_message(message_id):
    if not check_auth():
        return error_response("Unauthorized", 401)

    message = mock_messages.get(message_id)
    if not message:
        return error_response("Message not found", 404)

    data = request.get_json()

    # 更新消息字段
    if 'content' in data:
        message['content'] = data['content']
    if 'is_error' in data:
        message['is_error'] = data['is_error']
    if 'error_info' in data:
        message['error_info'] = data['error_info']
    if 'prompt_tokens' in data:
        message['prompt_tokens'] = data['prompt_tokens']
    if 'completion_tokens' in data:
        message['completion_tokens'] = data['completion_tokens']
    if 'total_cost' in data:
        message['total_cost'] = data['total_cost']

    message['updated_at'] = datetime.utcnow().isoformat()

    return success_response(message)

@app.route('/api/v1/messages/<int:message_id>', methods=['DELETE'])
def delete_message(message_id):
    if not check_auth():
        return error_response("Unauthorized", 401)

    if message_id in mock_messages:
        del mock_messages[message_id]
        return success_response({"message": "Message deleted"})
    else:
        return error_response("Message not found", 404)

if __name__ == '__main__':
    print("启动模拟Record API服务...")
    print("服务地址: http://localhost:5003")
    print("API密钥: admin-api-key-change-in-production")
    app.run(host='0.0.0.0', port=5003, debug=True)

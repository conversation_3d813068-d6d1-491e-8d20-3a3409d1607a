# .env.example - 环境变量配置示例文件
# 复制此文件为 .env 并根据实际情况修改配置

# =================== 数据库配置 ===================
# MariaDB数据库配置
MARIADB_HOST=*************
MARIADB_PORT=3306
MARIADB_USER=root
MARIADB_PASSWORD=rw80827
MARIADB_MODEL_DB=model_registry
MARIADB_CHAT_DB=chat_system

# =================== API服务配置 ===================
# Model API Service (端口5002) - 处理模型和聊天
MODEL_API_BASE_URL=http://localhost:5002
MODEL_API_KEY=sk-default-api-key-change-in-production

# Record API Service (端口5003) - 处理用户、对话、消息记录
RECORD_API_BASE_URL=http://localhost:5003
RECORD_API_KEY=admin-api-key-change-in-production

# =================== 应用配置 ===================
# Dash应用配置
DASH_HOST=0.0.0.0
DASH_PORT=8050
DASH_DEBUG=True

# 运行环境 (development/production/testing)
FLASK_ENV=development

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# =================== 安全配置 ===================
# JWT配置
JWT_SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# =================== 功能开关 ===================
# 是否使用Record API（True=使用API，False=直接访问数据库）
USE_RECORD_API=True

# 是否启用缓存
ENABLE_CACHE=True

# 是否启用调试模式
DEBUG_MODE=False

# =================== 生产环境配置 ===================
# 生产环境请务必修改以下配置：
# 1. 修改所有默认密钥和API密钥
# 2. 设置 FLASK_ENV=production
# 3. 设置 DEBUG_MODE=False
# 4. 设置 DASH_DEBUG=False
# 5. 使用强密码和安全的数据库配置

# =================== Docker配置 ===================
# 如果使用Docker，可能需要调整以下配置：
# MODEL_API_BASE_URL=http://api-service:5002
# RECORD_API_BASE_URL=http://record-service:5003
# MARIADB_HOST=database

# =================== 开发配置 ===================
# 开发环境可以使用以下配置：
# FLASK_ENV=development
# DEBUG_MODE=True
# DASH_DEBUG=True
# LOG_LEVEL=DEBUG

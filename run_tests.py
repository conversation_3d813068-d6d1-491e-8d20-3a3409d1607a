#!/usr/bin/env python3
# run_tests.py - 运行各种测试的脚本
import os
import sys
import subprocess
import time
import requests
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_service_health(url: str, service_name: str, timeout: int = 10) -> bool:
    """检查服务健康状态"""
    try:
        response = requests.get(f"{url}/health", timeout=timeout)
        if response.status_code == 200:
            logger.info(f"✓ {service_name} 服务正常运行")
            return True
        else:
            logger.error(f"✗ {service_name} 服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        logger.error(f"✗ {service_name} 服务不可达: {e}")
        return False


def check_dependencies():
    """检查依赖包"""
    logger.info("检查Python依赖包...")
    
    required_packages = [
        'requests',
        'dash',
        'dash-bootstrap-components',
        'sqlalchemy',
        'pymysql',
        'openai'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            logger.info(f"✓ {package}")
        except ImportError:
            logger.error(f"✗ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"缺少依赖包: {', '.join(missing_packages)}")
        logger.info("请运行: pip install -r requirements.txt")
        return False
    
    return True


def check_environment():
    """检查环境变量"""
    logger.info("检查环境变量配置...")
    
    required_env_vars = [
        'RECORD_API_BASE_URL',
        'RECORD_API_KEY',
        'MODEL_API_BASE_URL',
        'MODEL_API_KEY'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        value = os.environ.get(var)
        if not value:
            missing_vars.append(var)
            logger.warning(f"✗ {var} 未设置，将使用默认值")
        else:
            logger.info(f"✓ {var}")
    
    if missing_vars:
        logger.warning("建议创建 .env 文件并设置环境变量")
    
    return True


def run_record_api_test():
    """运行Record API集成测试"""
    logger.info("运行Record API集成测试...")
    
    try:
        result = subprocess.run([
            sys.executable, 'test_record_api_integration.py'
        ], capture_output=True, text=True, timeout=60)
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:", result.stderr)
        
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        logger.error("测试超时")
        return False
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        return False


def run_model_api_test():
    """运行Model API测试"""
    logger.info("运行Model API测试...")
    
    try:
        result = subprocess.run([
            sys.executable, 'test_api_call.py'
        ], capture_output=True, text=True, timeout=60)
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:", result.stderr)
        
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        logger.error("测试超时")
        return False
    except FileNotFoundError:
        logger.warning("test_api_call.py 文件不存在，跳过Model API测试")
        return True
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("InspirFlow API集成测试套件")
    print("=" * 60)
    
    # 1. 检查依赖
    logger.info("步骤 1: 检查依赖包")
    if not check_dependencies():
        logger.error("依赖检查失败，请安装缺少的包")
        return 1
    
    # 2. 检查环境变量
    logger.info("\n步骤 2: 检查环境变量")
    check_environment()
    
    # 3. 检查服务状态
    logger.info("\n步骤 3: 检查服务状态")
    
    # 获取配置
    record_api_url = os.environ.get('RECORD_API_BASE_URL', 'http://localhost:5003')
    model_api_url = os.environ.get('MODEL_API_BASE_URL', 'http://localhost:5002')
    
    services_ok = True
    
    # 检查Record API服务
    if not check_service_health(record_api_url, "Record API"):
        services_ok = False
        logger.warning("Record API服务不可用，相关测试可能失败")
    
    # 检查Model API服务
    if not check_service_health(model_api_url, "Model API"):
        logger.warning("Model API服务不可用，相关测试可能失败")
    
    # 4. 运行测试
    logger.info("\n步骤 4: 运行集成测试")
    
    test_results = []
    
    # Record API测试
    if services_ok:
        logger.info("\n--- Record API集成测试 ---")
        record_test_passed = run_record_api_test()
        test_results.append(("Record API集成", record_test_passed))
    else:
        logger.warning("跳过Record API测试（服务不可用）")
        test_results.append(("Record API集成", False))
    
    # Model API测试
    logger.info("\n--- Model API测试 ---")
    model_test_passed = run_model_api_test()
    test_results.append(("Model API", model_test_passed))
    
    # 5. 输出结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"{test_name:<20} {status}")
        if not passed:
            all_passed = False
    
    print("=" * 60)
    
    if all_passed:
        print("🎉 所有测试通过！系统集成成功！")
        print("\n接下来可以:")
        print("1. 启动主应用: python app.py")
        print("2. 访问 http://localhost:8050")
        return 0
    else:
        print("❌ 部分测试失败")
        print("\n故障排除建议:")
        print("1. 检查Record API服务是否启动 (端口5003)")
        print("2. 检查Model API服务是否启动 (端口5002)")
        print("3. 检查数据库连接配置")
        print("4. 检查API密钥配置")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

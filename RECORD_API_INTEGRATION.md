# Record API 集成说明

本文档说明如何将InspirFlow项目从直接数据库访问迁移到使用Record API Service进行用户、对话、消息管理。

## 概述

### 修改前的架构
```
InspirFlow App (8050) → 直接访问数据库 (MariaDB)
                     → Model API (5002) 用于AI聊天
```

### 修改后的架构
```
InspirFlow App (8050) → Record API (5003) 用于用户/对话/消息
                     → Model API (5002) 用于AI聊天
```

## 主要变更

### 1. 新增组件

- **record_api_client.py**: Record API客户端，封装所有API调用
- **config.py**: 统一配置管理
- **test_record_api_integration.py**: API集成测试脚本
- **run_tests.py**: 测试运行器
- **.env.example**: 环境变量配置示例

### 2. 修改的文件

- **db_operator.py**: 将数据库操作改为API调用
- **callbacks/auth_callbacks.py**: 认证逻辑（已自动适配）
- **callbacks/conversation_callbacks.py**: 对话管理（已自动适配）
- **callbacks/message_callbacks.py**: 消息处理（已自动适配）
- **openaiSDK.py**: AI集成（已自动适配）

### 3. 保持不变的接口

为了最小化对现有代码的影响，所有db_operator.py中的函数接口保持不变，只是内部实现改为调用Record API。

## 配置说明

### 环境变量

创建 `.env` 文件（参考 `.env.example`）：

```bash
# Record API配置
RECORD_API_BASE_URL=http://localhost:5003
RECORD_API_KEY=admin-api-key-change-in-production

# Model API配置
MODEL_API_BASE_URL=http://localhost:5002
MODEL_API_KEY=sk-default-api-key-change-in-production

# 功能开关
USE_RECORD_API=True
```

### 配置类

使用 `config.py` 中的配置类：

```python
from config import config

# 获取Record API配置
api_url = config.get_record_api_url()
headers = config.get_record_api_headers()
```

## API映射

### 用户管理

| 原函数 | Record API端点 | 说明 |
|--------|---------------|------|
| `verify_api_key(api_key)` | `GET /api/v1/users/by-api-key/{api_key}` | 验证API密钥 |
| `get_user_data(user_id)` | `GET /api/v1/users/{user_id}` | 获取用户信息 |
| `create_user(...)` | `POST /api/v1/users` | 创建用户 |
| `get_user_statistics(user_id)` | `GET /api/v1/users/{user_id}/stats` | 用户统计 |

### 对话管理

| 原函数 | Record API端点 | 说明 |
|--------|---------------|------|
| `get_conversation_data(conv_id)` | `GET /api/v1/conversations/{conv_id}` | 获取对话信息 |
| `create_new_conversation(user_id, title)` | `POST /api/v1/conversations` | 创建对话 |
| `get_user_conversations_list(user_id)` | `GET /api/v1/users/{user_id}/conversations` | 用户对话列表 |
| `delete_conversation(conv_id)` | `DELETE /api/v1/conversations/{conv_id}` | 删除对话 |

### 消息管理

| 原函数 | Record API端点 | 说明 |
|--------|---------------|------|
| `create_message(...)` | `POST /api/v1/messages` | 创建消息 |
| `get_conversation_messages_list(conv_id)` | `GET /api/v1/conversations/{conv_id}/messages` | 对话消息列表 |
| `update_message_content(msg_id, content)` | `PUT /api/v1/messages/{msg_id}` | 更新消息 |
| `delete_message(msg_id)` | `DELETE /api/v1/messages/{msg_id}` | 删除消息 |

## 部署和测试

### 1. 环境准备

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置正确的API地址和密钥

# 3. 确保Record API服务运行在5003端口
# 确保Model API服务运行在5002端口
```

### 2. 运行测试

```bash
# 运行完整测试套件
python run_tests.py

# 或单独运行Record API测试
python test_record_api_integration.py
```

### 3. 启动应用

```bash
python app.py
```

## 故障排除

### 常见问题

1. **Record API连接失败**
   - 检查Record API服务是否启动（端口5003）
   - 检查API密钥是否正确
   - 检查网络连接

2. **认证失败**
   - 确认RECORD_API_KEY配置正确
   - 确认Record API服务中存在对应的管理员账号

3. **数据不同步**
   - Record API和直接数据库访问的数据可能不一致
   - 建议完全切换到API模式

### 调试模式

启用调试模式获取更多日志：

```bash
export DEBUG_MODE=True
export LOG_LEVEL=DEBUG
python app.py
```

### 回退方案

如果需要回退到直接数据库访问：

```bash
export USE_RECORD_API=False
```

## 性能考虑

### API调用开销

- 每个数据库操作现在变成HTTP API调用
- 增加了网络延迟，但提高了系统解耦性
- 建议在生产环境中启用缓存

### 缓存策略

```python
# 在config.py中启用缓存
ENABLE_CACHE=True
```

### 连接池

Record API客户端使用requests库，自动管理连接池。

## 安全考虑

### API密钥管理

- 生产环境必须修改默认API密钥
- 使用环境变量存储敏感信息
- 定期轮换API密钥

### 网络安全

- 在生产环境中使用HTTPS
- 配置防火墙限制API访问
- 考虑使用VPN或内网访问

## 监控和日志

### 日志配置

```python
import logging
logging.basicConfig(level=logging.INFO)
```

### 监控指标

- API响应时间
- 错误率
- 请求量
- 数据库连接状态

## 未来改进

1. **缓存层**: 添加Redis缓存减少API调用
2. **重试机制**: 增加API调用失败重试
3. **熔断器**: 防止API服务故障影响主应用
4. **批量操作**: 支持批量API调用减少网络开销

## 支持

如有问题，请检查：

1. 服务状态：`python run_tests.py`
2. 日志文件：查看应用和API服务日志
3. 配置文件：确认所有配置项正确设置

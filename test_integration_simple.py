#!/usr/bin/env python3
# test_integration_simple.py - 简单的集成测试，不依赖外部服务
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

def test_imports():
    """测试所有模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        # 测试配置模块
        from config import config
        print("✓ config模块导入成功")
        print(f"  Record API URL: {config.RECORD_API_BASE_URL}")
        print(f"  Model API URL: {config.MODEL_API_BASE_URL}")
        
        # 测试Record API客户端
        from record_api_client import record_api_client
        print("✓ record_api_client模块导入成功")
        print(f"  API URL: {record_api_client.base_url}")
        
        # 测试db_operator
        import db_operator
        print("✓ db_operator模块导入成功")
        
        # 测试关键函数存在
        key_functions = [
            'verify_api_key',
            'get_user_data', 
            'create_user',
            'get_user_conversations_list',
            'create_new_conversation',
            'create_message',
            'get_conversation_messages_list',
            'update_message_content',
            'delete_message'
        ]
        
        missing_functions = []
        for func_name in key_functions:
            if hasattr(db_operator, func_name):
                print(f"  ✓ {func_name}")
            else:
                print(f"  ✗ {func_name}")
                missing_functions.append(func_name)
        
        if missing_functions:
            print(f"❌ 缺少函数: {', '.join(missing_functions)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_client_methods():
    """测试API客户端方法"""
    print("\n=== 测试API客户端方法 ===")
    
    try:
        from record_api_client import record_api_client
        
        # 测试方法存在
        methods = [
            'get_user_by_api_key',
            'get_user',
            'create_user',
            'get_user_conversations',
            'create_conversation',
            'create_message',
            'health_check'
        ]
        
        missing_methods = []
        for method_name in methods:
            if hasattr(record_api_client, method_name):
                print(f"  ✓ {method_name}")
            else:
                print(f"  ✗ {method_name}")
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"❌ 缺少方法: {', '.join(missing_methods)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ API客户端测试失败: {e}")
        return False


def test_callback_imports():
    """测试回调模块导入"""
    print("\n=== 测试回调模块导入 ===")
    
    try:
        # 测试回调模块
        from callbacks.auth_callbacks import register_auth_callbacks
        print("✓ auth_callbacks导入成功")
        
        from callbacks.conversation_callbacks import register_conversation_callbacks
        print("✓ conversation_callbacks导入成功")
        
        from callbacks.message_callbacks import register_message_callbacks
        print("✓ message_callbacks导入成功")
        
        # 测试OpenAI SDK
        from openaiSDK import OpenAISDK
        print("✓ openaiSDK导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 回调模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_app_structure():
    """测试应用结构"""
    print("\n=== 测试应用结构 ===")
    
    try:
        # 测试主应用模块
        import app
        print("✓ app模块导入成功")
        
        # 检查关键文件存在
        key_files = [
            'config.py',
            'record_api_client.py', 
            'db_operator.py',
            'app.py',
            'requirements.txt',
            '.env.example'
        ]
        
        missing_files = []
        for filename in key_files:
            if os.path.exists(filename):
                print(f"  ✓ {filename}")
            else:
                print(f"  ✗ {filename}")
                missing_files.append(filename)
        
        if missing_files:
            print(f"❌ 缺少文件: {', '.join(missing_files)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 应用结构测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        from record_api_client import record_api_client
        import db_operator
        
        # 测试API调用错误处理（服务不可用时）
        print("  测试API调用错误处理...")
        
        # 这些调用应该优雅地失败，而不是抛出异常
        result = db_operator.verify_api_key("test-key")
        print(f"  verify_api_key返回: {result}")
        
        result = db_operator.get_user_data(1)
        print(f"  get_user_data返回: {result}")
        
        result = db_operator.get_user_conversations_list(1)
        print(f"  get_user_conversations_list返回: {result}")
        
        print("✓ 错误处理测试通过（函数正确返回None/空列表而不是抛出异常）")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("InspirFlow API集成简单测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("API客户端方法", test_api_client_methods),
        ("回调模块导入", test_callback_imports),
        ("应用结构", test_app_structure),
        ("错误处理", test_error_handling)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"{test_name:<15} {status}")
        if not passed:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有测试通过！API集成代码结构正确！")
        print("\n注意事项:")
        print("- 当前测试不依赖外部服务")
        print("- 要完整测试功能，需要启动Record API服务(端口5003)")
        print("- 要测试AI聊天，需要启动Model API服务(端口5002)")
        return 0
    else:
        print("❌ 部分测试失败，请检查代码结构")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

import uuid
import string
import secrets
import json  # 确保导入json模块
import hashlib
import zlib
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy import inspect
from sqlalchemy.exc import SQLAlchemyError
from db_models import (
    User, Conversation, Message, Transaction,
    MessageContent, RenderedContent,  # 只保留聊天相关的类
    chat_db_engine, logger, ChatDBSession
)
from utils.render_markdown import render_content_to_html
from record_api_client import record_api_client  # 导入Record API客户端

# 创建会话工厂
chat_db_session = ChatDBSession()

def verify_api_key(api_key: str) -> Optional[Dict[str, Any]]:
    """验证API密钥并返回用户数据"""
    try:
        return record_api_client.verify_api_key(api_key)
    except Exception as e:
        logger.error(f"验证API密钥失败: {e}")
        return None


# =================== 用户相关操作 ===================

def get_user_data(user_id: int) -> Optional[Dict[str, Any]]:
    """获取用户信息（返回字典而非ORM对象）"""
    try:
        return record_api_client.get_user_data(user_id)
    except Exception as e:
        logger.error(f"获取用户数据失败: {e}")
        return None


def get_user_temperature(user_id: int, default: float = 0.7) -> float:
    """获取用户当前温度设置"""
    try:
        return record_api_client.get_user_temperature(user_id, default)
    except Exception as e:
        logger.error(f"获取用户温度设置失败: {e}")
        return default


def get_user_model_name(user_id: int) -> Optional[str]:
    """获取用户当前模型名称"""
    try:
        return record_api_client.get_user_model_name(user_id, "gpt-3.5-turbo")
    except Exception as e:
        logger.error(f"获取用户模型名称失败: {e}")
        return "gpt-3.5-turbo"

def get_user_model_id(user_id: int) -> Optional[int]:
    """获取用户当前模型ID（已废弃，保留兼容性）"""
    # 这个函数保留是为了兼容性，但不再使用
    return None


def update_user_model_name_preference(user_id: int, model_name: str) -> bool:
    """更新用户的模型名称偏好设置"""
    try:
        result = record_api_client.update_user(user_id, current_model_name=model_name)
        return result is not None
    except Exception as e:
        logger.error(f"更新用户模型名称偏好失败: {e}")
        return False

def update_user_model_preference(user_id: int, model_id: int) -> bool:
    """更新用户的模型偏好设置（已废弃，保留兼容性）"""
    # 这个函数保留是为了兼容性，但不再使用
    return True


def update_user_temperature_preference(user_id: int, temperature: float) -> bool:
    """更新用户的温度设置"""
    try:
        result = record_api_client.update_user(user_id, current_temperature=temperature)
        return result is not None
    except Exception as e:
        logger.error(f"更新用户温度设置失败: {e}")
        return False


def update_user_conversation_preference(user_id: int, conversation_id: int) -> bool:
    """更新用户的对话偏好设置"""
    try:
        result = record_api_client.update_user(user_id, current_conversation_id=conversation_id)
        return result is not None
    except Exception as e:
        logger.error(f"更新用户对话偏好失败: {e}")
        return False


def update_user_mathjax_preference(user_id: int, use_mathjax: bool) -> bool:
    """更新用户MathJax渲染偏好"""
    try:
        result = record_api_client.update_user(user_id, mathjax=use_mathjax)
        return result is not None
    except Exception as e:
        logger.error(f"更新用户MathJax偏好失败: {e}")
        return False


def get_user_statistics(user_id: int) -> Optional[Dict[str, Any]]:
    """获取用户统计数据"""
    try:
        return record_api_client.get_user_statistics(user_id)
    except Exception as e:
        logger.error(f"获取用户统计数据失败: {e}")
        return None


# =================== 模型相关操作（已移除数据库依赖，改为API服务）===================
# 这些函数保留是为了兼容性，但实际功能已移至API服务

def get_available_models() -> List[Dict[str, Any]]:
    """获取所有可用模型（已废弃，使用API服务）"""
    # 这个函数保留是为了兼容性，实际应该使用callbacks/model_callbacks.py中的get_available_models_from_api
    logger.warning("get_available_models函数已废弃，请使用API服务获取模型列表")
    return []

def get_model_data(model_id: int) -> Optional[Dict[str, Any]]:
    """根据ID查找模型（已废弃，使用API服务）"""
    # 这个函数保留是为了兼容性
    logger.warning("get_model_data函数已废弃，请使用API服务获取模型信息")
    return None

def get_AU_from_model_name(model_name: str) -> Tuple[str, str]:
    """根据模型名称获取API配置（使用新的API服务）"""
    # 现在所有模型都使用统一的API服务
    from callbacks.model_callbacks import API_BASE_URL, API_KEY
    return API_KEY, API_BASE_URL


# =================== 对话相关操作 ===================

def get_conversation_data(conversation_id: int) -> Optional[Dict[str, Any]]:
    """获取指定ID的对话数据（字典形式）"""
    try:
        return record_api_client.get_conversation_data(conversation_id)
    except Exception as e:
        logger.error(f"获取对话失败: {e}")
        return None


def get_user_conversations_list(user_id: int) -> List[Dict[str, Any]]:
    """获取用户的所有对话（字典列表）"""
    try:
        return record_api_client.get_user_conversations_list(user_id)
    except Exception as e:
        logger.error(f"获取用户对话列表失败: {e}")
        return []


def create_new_conversation(user_id: int, title: str = '新对话') -> Optional[Dict[str, Any]]:
    """创建新对话并返回数据字典"""
    try:
        return record_api_client.create_new_conversation(user_id, title)
    except Exception as e:
        logger.error(f"创建新对话失败: {e}")
        return None


def update_conversation_title(conversation_id: int, title: str) -> bool:
    """更新对话标题"""
    try:
        return record_api_client.update_conversation_title(conversation_id, title)
    except Exception as e:
        logger.error(f"更新对话标题失败: {e}")
        return False


def delete_conversation(conversation_id: int) -> bool:
    """删除指定ID的对话及其所有消息"""
    try:
        return record_api_client.delete_conversation(conversation_id)
    except Exception as e:
        logger.error(f"删除对话失败: {e}")
        return False


def set_current_conversation(user_id: int, conversation_id: int) -> bool:
    """设置用户的当前对话"""
    try:
        return record_api_client.set_current_conversation(user_id, conversation_id)
    except Exception as e:
        logger.error(f"设置当前对话失败: {e}")
        return False


# =================== 消息相关操作 ===================

def get_message_data(message_id: int) -> Optional[Dict[str, Any]]:
    """获取指定消息数据（字典形式）"""
    try:
        return record_api_client.get_message_data(message_id)
    except Exception as e:
        logger.error(f"获取消息数据失败: {e}")
        return None


def get_conversation_messages_list(conversation_id: int) -> List[Dict[str, Any]]:
    """获取对话的所有消息（字典列表）"""
    try:
        return record_api_client.get_conversation_messages_list(conversation_id)
    except Exception as e:
        logger.error(f"获取对话消息列表失败: {e}")
        return []


def load_conversation_messages_by_id(conversation_id: int) -> List[Dict[str, str]]:
    """加载对话历史，转换为AI接口格式"""
    try:
        return record_api_client.load_conversation_messages_by_id(conversation_id)
    except Exception as e:
        logger.error(f"加载对话历史失败: {e}")
        return []


def update_message_token_count(message_id: int, prompt_tokens: int, completion_tokens: int) -> bool:
    """更新消息的token计数"""
    try:
        return record_api_client.update_message_token_count(message_id, prompt_tokens, completion_tokens)
    except Exception as e:
        logger.error(f"更新消息token计数失败: {e}")
        return False


def delete_message(message_id: int) -> bool:
    """删除指定ID的单条消息"""
    try:
        return record_api_client.delete_message(message_id)
    except Exception as e:
        logger.error(f"删除消息失败: {e}")
        return False


# =================== 账户金额相关 ===================

def deposit_to_user(user_id: int, amount: float, description: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """为用户充值"""
    session = ChatDBSession()
    try:
        user = session.query(User).filter_by(id=user_id).first()
        if not user:
            logger.warning(f"未找到用户ID: {user_id}")
            return None

        # 更新用户余额
        amount_decimal = Decimal(str(amount))
        user.total_deposited += amount_decimal
        user.current_balance += amount_decimal

        # 创建交易记录
        transaction = Transaction(
            user_id=user_id,
            transaction_type='deposit',
            amount=amount_decimal,
            balance_after=user.current_balance,
            description=description or f"充值 ${amount}"
        )

        session.add(transaction)
        session.commit()
        logger.info(f"用户 {user_id} 成功充值 ${amount}，当前余额: ${user.current_balance}")
        return get_user_data(user_id)
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"用户充值失败: {e}")
        return None
    finally:
        session.close()


def check_user_balance(user_id: int, model_name: str, estimated_tokens: int = 1000) -> Tuple[bool, str]:
    """检查用户余额是否足够支付预估的token消费（使用默认价格）"""
    chat_session = ChatDBSession()
    try:
        user = chat_session.query(User).filter_by(id=user_id).first()
        if not user:
            return False, "用户不存在"

        # 使用默认价格进行预估（每1000个token的价格）
        # 这些价格可以根据实际情况调整
        default_input_price = Decimal('0.001')  # $0.001 per 1000 tokens
        default_output_price = Decimal('0.002')  # $0.002 per 1000 tokens
        
        # 根据模型名称调整价格
        if "gpt-4" in model_name.lower():
            default_input_price = Decimal('0.03')   # GPT-4价格更高
            default_output_price = Decimal('0.06')
        elif "claude" in model_name.lower():
            default_input_price = Decimal('0.008')
            default_output_price = Decimal('0.024')
        
        # 计算预估成本 (假设输入输出token数量相等)
        estimated_cost = (default_input_price + default_output_price) * Decimal(estimated_tokens) / 1000

        # 检查余额
        if user.current_balance < estimated_cost:
            return False, f"余额不足。当前余额: ${float(user.current_balance)}, 预估成本: ${float(estimated_cost)}"

        return True, "余额充足"
    except SQLAlchemyError as e:
        logger.error(f"检查用户余额失败: {e}")
        return False, f"系统错误: {str(e)}"
    finally:
        chat_session.close()


def calculate_message_cost(message_id: int) -> Optional[float]:
    """计算消息成本并更新用户余额（使用默认价格）"""
    chat_session = ChatDBSession()
    try:
        message = chat_session.query(Message).filter_by(id=message_id).first()
        if not message:
            logger.warning(f"未找到消息ID: {message_id}")
            return None

        # 获取关联的对话和用户
        conversation = chat_session.query(Conversation).filter_by(id=message.conversation_id).first()
        if not conversation:
            logger.warning(f"未找到对话")
            return None

        user = chat_session.query(User).filter_by(id=conversation.user_id).first()
        if not user:
            logger.warning(f"未找到用户")
            return None

        # 使用默认价格计算成本 (价格是每1000个token的价格)
        prompt_tokens = message.prompt_tokens or 0
        completion_tokens = message.completion_tokens or 0

        # 默认价格，可以根据模型名称进行调整
        default_input_price = Decimal('0.001')  # $0.001 per 1000 tokens
        default_output_price = Decimal('0.002')  # $0.002 per 1000 tokens
        
        # 根据模型名称调整价格
        model_name = message.model_name or "gpt-3.5-turbo"
        if "gpt-4" in model_name.lower():
            default_input_price = Decimal('0.03')   # GPT-4价格更高
            default_output_price = Decimal('0.06')
        elif "claude" in model_name.lower():
            default_input_price = Decimal('0.008')
            default_output_price = Decimal('0.024')

        prompt_cost = default_input_price * Decimal(prompt_tokens) / 1000
        completion_cost = default_output_price * Decimal(completion_tokens) / 1000
        total_cost = prompt_cost + completion_cost

        # 更新消息成本
        message.prompt_cost = prompt_cost
        message.completion_cost = completion_cost
        message.total_cost = total_cost

        # 更新用户统计和余额
        user.total_prompt_tokens += prompt_tokens
        user.total_completion_tokens += completion_tokens
        user.total_spent += total_cost
        user.current_balance -= total_cost

        # 创建交易记录
        transaction = Transaction(
            user_id=user.id,
            transaction_type='consumption',
            amount=total_cost,
            balance_after=user.current_balance,
            message_id=message_id,
            description=f"消息ID {message_id} 消费: 输入 {prompt_tokens} tokens, 输出 {completion_tokens} tokens (模型: {model_name})"
        )

        chat_session.add(transaction)
        chat_session.commit()

        logger.info(f"消息 {message_id} 成本计算完成: ${total_cost}, 用户余额: ${user.current_balance}")
        return float(total_cost)
    except SQLAlchemyError as e:
        chat_session.rollback()
        logger.error(f"计算消息成本失败: {e}")
        return None
    finally:
        chat_session.close()


# =================== 事务相关操作 ===================

def get_user_transactions(user_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """获取用户的交易记录"""
    session = ChatDBSession()
    try:
        transactions = session.query(Transaction).filter_by(user_id=user_id) \
            .order_by(Transaction.created_at.desc()).limit(limit).offset(offset).all()

        return [{
            "id": tx.id,
            "user_id": tx.user_id,
            "transaction_type": tx.transaction_type,
            "amount": float(tx.amount),
            "balance_after": float(tx.balance_after),
            "description": tx.description,
            "message_id": tx.message_id,
            "created_at": tx.created_at.isoformat() if tx.created_at else None
        } for tx in transactions]
    except SQLAlchemyError as e:
        logger.error(f"获取用户交易记录失败: {e}")
        return []
    finally:
        session.close()


# =================== 用户管理相关 ===================

def generate_api_key(prefix: str = "sk-") -> str:
    """生成一个唯一的API密钥"""
    # 生成一个随机的UUID部分
    uuid_part = str(uuid.uuid4()).replace('-', '')

    # 生成一个随机的字符串部分（16个字符）
    alphabet = string.ascii_letters + string.digits
    random_part = ''.join(secrets.choice(alphabet) for _ in range(16))
    # 组合前缀和两个随机部分
    api_key = f"{prefix}{uuid_part}{random_part}"
    return api_key


def create_user(
        permission: int = 1,
        current_model_name: str = "gpt-3.5-turbo",
        initial_balance: float = 10.0,
        # 保留current_model_id参数以兼容旧代码
        current_model_id: int = None) -> (
        Tuple)[Optional[Dict[str, Any]], Optional[str]]:
    """创建新用户"""
    try:
        # 生成API密钥
        api_key = generate_api_key()

        # 准备用户数据
        user_data = {
            'api_key': api_key,
            'permission': permission,
            'current_balance': initial_balance,
            'total_deposited': initial_balance,
            'current_temperature': 0.7,
            'mathjax': False
        }

        # 创建用户
        user = record_api_client.create_user(**user_data)
        if not user:
            return None, None

        # 创建初始对话
        title = "欢迎使用AI对话系统" if permission == 9 else "开始使用AI助手"
        conversation = record_api_client.create_conversation(user['id'], title)
        if not conversation:
            return None, None

        # 设置为当前对话
        record_api_client.set_current_conversation(user['id'], conversation['id'])

        # 如果是管理员，添加初始消息
        if permission == 9:
            record_api_client.create_message(
                conversation_id=conversation['id'],
                role="assistant",
                content="欢迎使用AI对话系统！我已准备好回答您的问题。",
                model_id=1,  # 默认模型ID
                prompt_tokens=0,
                completion_tokens=0,
                total_cost=0.0
            )

        logger.info(f"用户已创建，权限级别: {permission}, API密钥: {api_key}")

        # 返回用户数据和API密钥
        return user, api_key

    except Exception as e:
        logger.error(f"创建用户失败: {e}")
        return None, None


# =================== 管理员功能 ===================

def is_admin_user(user_id: int) -> bool:
    """检查用户是否具有管理员权限 (permission=9)"""
    session = ChatDBSession()
    try:
        user = session.get(User, user_id)
        return user is not None and user.permission == 9
    except SQLAlchemyError as e:
        logger.error(f"检查管理员权限失败: {e}")
        return False
    finally:
        session.close()


def get_all_users(admin_id: int) -> Optional[List[Dict[str, Any]]]:
    """获取所有用户列表（仅限管理员）"""
    if not is_admin_user(admin_id):
        logger.warning(f"非管理员用户 {admin_id} 尝试访问用户列表")
        return None

    session = ChatDBSession()
    try:
        users = session.query(User).all()
        return [{
            "id": user.id,
            "api_key": user.api_key[:10] + "..." + user.api_key[-5:],  # 只返回部分API密钥
            "permission": user.permission,
            "is_active": user.is_active,
            "current_balance": float(user.current_balance) if user.current_balance else 0.0,
            "total_spent": float(user.total_spent) if user.total_spent else 0.0,
            "total_deposited": float(user.total_deposited) if user.total_deposited else 0.0,
            "created_at": user.created_at.isoformat() if user.created_at else None,
        } for user in users]
    except SQLAlchemyError as e:
        logger.error(f"获取用户列表失败: {e}")
        return None
    finally:
        session.close()


def toggle_user_active_status(admin_id: int, user_id: int) -> bool:
    """切换用户活跃状态（启用/禁用用户）(仅限管理员)"""
    if not is_admin_user(admin_id):
        logger.warning(f"非管理员用户 {admin_id} 尝试切换用户 {user_id} 的活跃状态")
        return False

    session = ChatDBSession()
    try:
        user = session.get(User, user_id)
        if not user:
            return False

        # 不允许禁用自己（管理员）
        if user.id == admin_id:
            logger.warning(f"管理员 {admin_id} 尝试禁用自己的账户")
            return False

        # 切换状态
        user.is_active = not user.is_active
        session.commit()
        return True
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"切换用户活跃状态失败: {e}")
        return False
    finally:
        session.close()


def admin_add_user_balance(admin_id: int, user_id: int, amount: float, description: str = None) -> bool:
    """管理员为用户充值（仅限管理员）"""
    if not is_admin_user(admin_id):
        logger.warning(f"非管理员用户 {admin_id} 尝试为用户 {user_id} 充值")
        return False

    if amount <= 0:
        return False

    session = ChatDBSession()
    try:
        user = session.get(User, user_id)
        if not user:
            return False

        # 更新用户余额
        amount_decimal = Decimal(str(amount))
        user.total_deposited += amount_decimal
        user.current_balance += amount_decimal

        # 创建交易记录
        transaction = Transaction(
            user_id=user_id,
            transaction_type='admin_deposit',
            amount=amount_decimal,
            balance_after=user.current_balance,
            description=description or f"管理员充值: ${amount}"
        )

        session.add(transaction)
        session.commit()
        logger.info(f"管理员 {admin_id} 为用户 {user_id} 充值 ${amount}，用户当前余额: ${user.current_balance}")
        return True
    except SQLAlchemyError as e:
        session.rollback()
        logger.error(f"管理员充值失败: {e}")
        return False
    finally:
        session.close()


def admin_create_new_user(
        admin_id: int,
        permission: int = 1,
        initial_balance: float = 10.0) \
        -> Optional[Tuple[Dict[str, Any], str]]:
    """管理员创建新用户（仅限管理员）"""
    if not is_admin_user(admin_id):
        logger.warning(f"非管理员用户 {admin_id} 尝试创建用户")
        return None

    # 使用现有的create_user函数和默认模型名称
    return create_user(permission=permission, initial_balance=initial_balance, current_model_name="gpt-3.5-turbo")



# 在 db_operator.py 中添加和修改以下函数

import hashlib
import zlib
from utils.render_markdown import render_content_to_html

def get_content_hash(content):
    """计算内容的哈希值"""
    return hashlib.sha256(content.encode('utf-8')).hexdigest()

def get_message_content(message_id):
    """获取消息内容"""
    session = ChatDBSession()
    try:
        content_obj = session.query(MessageContent).filter_by(message_id=message_id).first()
        if content_obj:
            return content_obj.content
        return None
    except Exception as e:
        logger.error(f"获取消息内容失败: {e}")
        return None
    finally:
        session.close()

def update_message_content(message_id, new_content, is_error=False, error_info=None):
    """更新消息内容"""
    try:
        # 准备更新数据
        update_data = {
            'content': new_content,
            'is_error': is_error
        }

        if error_info is not None:
            update_data['error_info'] = error_info

        # 通过API更新消息
        result = record_api_client.update_message(message_id, **update_data)
        return result
    except Exception as e:
        logger.error(f"更新消息内容失败: {e}")
        return None

# create_message函数已在上面定义，这里删除重复定义

# 渲染内容相关函数
def store_rendered_content(message_id, content):
    """存储消息的渲染内容"""
    session = ChatDBSession()
    try:
        # 检查消息是否存在
        message = session.get(Message, message_id)
        if not message:
            logger.warning(f"未找到消息ID: {message_id}")
            return False

        # 渲染内容（带MathJax和不带MathJax）
        html_with_mathjax = render_content_to_html(content, detect_markdown=True, mathjax=True)
        html_without_mathjax = render_content_to_html(content, detect_markdown=True, mathjax=False)

        # 压缩HTML内容以节省空间
        compressed_with_mathjax = zlib.compress(html_with_mathjax.encode('utf-8'))
        compressed_without_mathjax = zlib.compress(html_without_mathjax.encode('utf-8'))

        # 检查是否已存在渲染内容
        rendered = session.query(RenderedContent).filter_by(message_id=message_id).first()

        if rendered:
            # 更新现有记录
            rendered.rendered_with_mathjax = compressed_with_mathjax
            rendered.rendered_without_mathjax = compressed_without_mathjax
            rendered.updated_at = datetime.utcnow()
        else:
            # 创建新记录
            rendered = RenderedContent(
                message_id=message_id,
                rendered_with_mathjax=compressed_with_mathjax,
                rendered_without_mathjax=compressed_without_mathjax
            )
            session.add(rendered)

        session.commit()
        return True
    except Exception as e:
        session.rollback()
        logger.error(f"存储渲染内容失败: {e}")
        return False
    finally:
        session.close()

def get_rendered_content(message_id, mathjax=True):
    """获取消息的渲染内容"""
    session = ChatDBSession()
    try:
        rendered = session.query(RenderedContent).filter_by(message_id=message_id).first()
        if not rendered:
            return None

        # 选择适当的渲染内容
        compressed_content = rendered.rendered_with_mathjax if mathjax else rendered.rendered_without_mathjax

        # 解压缩内容
        if compressed_content:
            return zlib.decompress(compressed_content).decode('utf-8')
        return None
    except Exception as e:
        logger.error(f"获取渲染内容失败: {e}")
        return None
    finally:
        session.close()

def clear_rendered_content(message_id):
    """清除消息的渲染缓存"""
    session = ChatDBSession()
    try:
        rendered = session.query(RenderedContent).filter_by(message_id=message_id).first()
        if rendered:
            session.delete(rendered)
            session.commit()
            return True
        return False
    except Exception as e:
        session.rollback()
        logger.error(f"清除渲染缓存失败: {e}")
        return False
    finally:
        session.close()
